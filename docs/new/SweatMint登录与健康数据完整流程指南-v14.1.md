# SweatMint登录与健康数据流程指南 v2.3

**文档版本**: v2.3
**创建日期**: 2025-07-23
**适用范围**: SweatMint健康激励应用流程管理
**系统状态**: 优化流程设计，确保数据真实性和用户体验

---

## 🎯 文档概览

### 文档目标
本文档提供SweatMint登录与健康数据处理的**简化流程指南**，重点说明：
- **3步骤流程**: 认证验证 → 数据准备 → 界面加载
- **组件职责**: 明确各组件的核心职责和协作关系
- **场景处理**: 不同触发场景的统一处理逻辑
- **双Token机制**: 集成Access-Token + Refresh-Token认证体系

### 架构简化原则
- **职责单一**: 每个组件专注核心功能，避免职责过载
- **流程简化**: 减少不必要的步骤，提升系统稳定性
- **状态统一**: 统一状态管理，避免状态不一致问题
- **错误隔离**: 完善的错误边界，确保系统健壮性

### 核心设计理念
- **数据真实性**: 健康数据不使用缓存，实时获取确保数据准确性
- **用户体验优先**: 即使数据准备失败也能进入UI，通过占位符处理异常
- **统一流程**: 所有场景都遵循认证验证→数据准备→界面加载的3步骤流程
- **安全第一**: 基于双Token机制的安全认证体系

---

## 📋 简化3步骤流程

### 🔐 步骤1: 认证验证
**执行位置**: SplashScreen
**负责组件**: AuthProvider
**目标**: 基于双Token机制验证用户身份

**双Token认证机制**:
- **Access-Token**: 15分钟有效期，用于API访问
- **Refresh-Token**: 14天有效期，用于Token刷新
- **自动刷新**: 剩余时间≤7.5分钟时自动刷新
- **设备绑定**: 基于device_id的单设备登录策略

**执行逻辑**:
```
Token检查 → 有效性验证 → 自动刷新 → 认证结果
    │           │           │          │
    ▼           ▼           ▼          ▼
 本地存储    时间校验    网络请求   状态更新
```

**SplashScreen进度机制**:
- **进度条显示**: 0%-100%进度条，100%完成后进入主界面
- **步骤1进度**: 0%-30%，认证验证阶段
- **失败处理**: 认证失败时仍继续到步骤2，通过占位符显示状态

**处理结果**:
- **认证成功**: 继续步骤2，进度30%→50%
- **认证失败**: 继续步骤2，标记为未认证状态，最终跳转登录页
- **设备冲突**: 继续步骤2，标记冲突状态，最终显示冲突提示
- **网络异常**: 继续步骤2，标记网络异常，最终提供重试选项

---

### � 步骤2: 数据准备
**执行位置**: SplashScreen
**负责组件**: HealthDataManager
**目标**: 准备健康数据和会话状态

**核心功能**:
1. **健康权限检查**: 检查步数、距离、卡路里三种权限状态
2. **会话连续性判断**: 处理应用重启、唤醒、跨天等场景
3. **基线数据管理**: 为已授权权限设置健康数据基线
4. **健康数据同步**: 获取当前健康数据并计算增量

**权限检查逻辑**:
- 使用HKStatisticsQuery独立验证三种权限
- 权限状态: authorized(已授权) / notDetermined(未授权) / denied(已拒绝)
- 三种权限完全独立，互不影响
- **健康数据不使用缓存**: 每次都实时检查，确保数据真实性
- **失败处理**: 权限检查失败时标记为未授权，不阻塞后续流程

**会话管理策略**:
```
场景判断 → 会话创建 → 状态持久化 → 基线设置 → 数据同步
    │          │           │           │          │
    ▼          ▼           ▼           ▼          ▼
 应用状态   会话初始化   状态存储    基线计算   增量获取
```

**会话创建机制**:
- **会话ID生成**: 使用UUID v4确保唯一性
- **会话数据结构**: sessionId, userId, deviceId, createdAt, expiresAt
- **会话类型标记**: RESTART/CROSS_DAY/TIMEOUT/RESUME/TIMER
- **会话状态持久化**: 使用flutter_secure_storage加密存储
- **会话恢复机制**: 应用启动时自动恢复有效会话

**场景优先级** (按优先级排序):
1. **应用重启**: 强制创建新会话，清理旧会话状态
2. **跨天检测**: 结算前一天数据，创建跨天新会话
3. **会话超时**: >4小时无活动，创建超时新会话
4. **正常延续**: 恢复现有会话，验证会话有效性

**SplashScreen进度控制**:
- **步骤2进度**: 30%-80%，数据准备阶段
- **权限检查**: 30%-50%
- **会话处理**: 50%-65%
- **数据同步**: 65%-80%

**数据输出**:
- 权限状态映射表 (steps/distance/calories)
- 会话连续性结果和基线数据
- 健康数据同步结果和增量计算
- 异常情况记录和错误信息
- **失败处理**: 即使数据准备失败，也继续到步骤3，通过占位符显示

---

### 🎨 步骤3: 界面加载
**执行位置**: MainLayoutScreen
**负责组件**: UI Controllers
**目标**: 加载主界面并处理用户交互

**SplashScreen进度完成**:
- **步骤3进度**: 80%-100%，界面加载阶段
- **100%完成**: SplashScreen消失，进入MainLayoutScreen

**执行逻辑**:
- 基于步骤1-2的结果加载主界面数据
- **异常状态处理**: 认证失败时跳转登录页，其他异常显示占位符
- 根据权限状态决定是否显示权限引导
- 处理用户的权限授权操作
- 实现健康数据的实时更新

**UI显示规则**:
- **已授权数据**: 显示实际运动数值和进度
- **未授权数据**: 显示"--"占位符和引导提示
- **加载状态**: 显示加载指示器和进度反馈
- **错误状态**: 显示友好的错误信息和重试选项

**权限引导机制**:
```
权限状态检查 → 引导决策 → 用户交互 → 状态更新
      │            │          │          │
      ▼            ▼          ▼          ▼
   步骤2结果    是否需要引导   权限授权   数据刷新
```

**数据更新策略**:
- **定时同步**: 前台运行时每2分钟自动同步
- **手动刷新**: 用户下拉刷新触发同步
- **权限变化**: 权限授权后立即同步数据
- **后台恢复**: 从后台返回时检查数据更新

---

## 🔄 核心场景流程

### 📱 用户登录场景
**触发时机**: 用户在登录页面输入账号密码并点击登录

**执行流程**:
1. **登录验证**: 后端验证账号密码，返回双Token
2. **进入3步骤流程**: 登录成功后直接执行认证验证→数据准备→界面加载
3. **首次登录vs重新登录**: 无差别处理，都执行完整3步骤流程
4. **健康数据不使用缓存**: 确保获取最新真实数据

**登录失败处理**:
- 显示错误信息，停留在登录页面
- 不执行后续3步骤流程

### 🚪 用户登出场景
**触发时机**: 用户点击登出按钮或系统检测到需要强制登出

**执行流程**:
1. **后端确认**: 调用登出API，服务端清理Token和会话
2. **会话销毁**: 清理本地会话数据和状态
3. **Token清理**: 清除本地存储的Access-Token和Refresh-Token
4. **状态重置**: 重置所有Provider状态和健康数据
5. **返回登录页**: 跳转到登录页面

**强制登出场景**:
- Token刷新失败且无法恢复
- 检测到设备冲突
- 服务端返回需要重新登录的错误码

### 🌅 应用唤醒场景
**触发时机**: 应用从后台返回前台

**执行流程**:
1. **时间检查**: 检查距离上次活跃时间是否超过4小时
2. **超过4小时**: 重建会话，执行完整3步骤流程
3. **未超过4小时**: 直接进入健康数据同步，然后界面刷新
4. **都遵循统一流程**: 最终都通过认证验证→数据准备→界面加载

**简化处理逻辑**:
- 不区分冷启动和热启动，唤醒就是从后台返回前台
- 基于4小时规则决定是否重建会话
- 健康数据不使用缓存，确保数据真实性

### ⏰ 定时同步场景
**触发时机**: 用户在app前台连续2分钟触发健康数据同步

**执行条件**:
- 会话连续且认证状态有效
- 应用在前台运行状态

**执行流程**:
1. **直接数据同步**: 跳过认证验证和会话检查，直接同步健康数据
2. **失败重试**: 最多重试3次，超过则等待下一周期
3. **静默刷新**: 成功后静默更新前端数据显示
4. **资源控制**: 网络异常时暂停定时同步，恢复后继续

---

## 🏗️ 核心组件职责

### 🔐 AuthProvider - 认证状态管理
**核心职责**:
- 双Token认证机制的完整实现
- 用户登录/登出流程控制
- Token自动刷新和设备冲突处理
- 认证状态的安全持久化

**主要功能**:
- `checkAuthStatus()`: 检查当前认证状态和Token有效性
- `refreshTokenIfNeeded()`: 智能Token刷新机制
- `handleDeviceConflict()`: 处理设备冲突和强制登出
- `clearAuthData()`: 安全清除认证数据

**Token管理策略**:
- **Access-Token**: 15分钟有效期，剩余≤7.5分钟自动刷新
- **Refresh-Token**: 14天有效期，支持轮换和黑名单机制
- **设备绑定**: 基于device_id的单设备登录策略
- **安全存储**: 使用flutter_secure_storage加密存储

---

### � SessionManager - 会话生命周期管理
**核心职责**:
- 会话的创建、恢复、验证和清理
- 会话状态的持久化存储
- 4小时超时规则的执行
- 与认证状态的同步

**主要功能**:
- `createSession()`: 创建新会话，生成唯一ID
- `restoreSession()`: 恢复已存在的有效会话
- `validateSession()`: 验证会话有效性和4小时超时
- `clearSession()`: 清理会话数据

**会话数据结构**:
```typescript
interface SessionData {
  sessionId: string;      // 唯一会话标识(UUID v4)
  userId: string;         // 关联用户ID
  createdAt: Date;        // 创建时间
  lastActiveAt: Date;     // 最后活跃时间
}
```

**简化设计原则**:
- **核心功能优先**: 专注会话生命周期管理，移除监控诊断功能
- **状态同步**: 与AuthProvider状态保持同步
- **失败降级**: 会话操作失败时不阻塞主流程

---

### �📊 HealthDataManager - 健康数据管理
**核心职责**:
- 健康权限的检查和管理
- 健康数据基线设置和维护
- 健康数据同步和增量计算
- 与SessionManager协作处理基线集成

**主要功能**:
- `checkHealthPermissions()`: 检查三种健康权限状态
- `setupSessionBaseline()`: 基于会话信息设置基线
- `syncHealthData()`: 同步健康数据并计算增量
- `validateHealthData()`: 验证健康数据的合理性和完整性

**权限管理原则**:
- **独立检查**: 步数、距离、卡路里权限完全独立
- **实时验证**: 不依赖缓存，每次实时检查
- **安全第一**: 未授权权限绝不强制获取数据
- **用户友好**: 提供清晰的权限引导和说明

**基线会话集成**:
- **松耦合设计**: 基线管理独立于会话生命周期
- **会话关联**: 基线数据与sessionId关联，支持多会话
- **异常隔离**: 基线异常不影响会话正常运行
- **数据恢复**: 支持基线数据的备份和恢复机制

---

### �️ UI Controllers - 界面控制器
**核心职责**:
- 应用启动页面和主界面的控制
- 用户交互处理和状态反馈
- 权限引导和用户教育
- 健康数据的可视化展示

**SplashScreen控制器**:
- 执行步骤1-2的启动流程
- 显示启动进度和状态反馈
- 处理启动异常和错误恢复
- 控制向主界面的跳转时机

**MainLayoutScreen控制器**:
- 执行步骤3的界面加载
- 处理健康数据的实时展示
- 管理权限引导和用户交互
- 实现数据的定时更新和手动刷新

**UI显示策略**:
- **已授权数据**: 显示实际数值和进度条
- **未授权数据**: 显示"--"占位符和引导按钮
- **加载状态**: 显示加载动画和进度提示
- **错误状态**: 显示友好错误信息和重试选项

---

## 🔄 场景优先级处理机制

### 场景检测顺序
按以下顺序检测场景，一旦匹配立即执行，不再检测后续场景：

**检测顺序** (严格按序执行):
1. **用户登录**: 用户主动登录操作，执行完整3步骤流程
2. **用户登出**: 用户主动登出或强制登出，执行登出流程
3. **应用启动**: 应用从完全关闭状态启动，执行完整3步骤流程
4. **应用唤醒**: 从后台返回前台，基于4小时规则决定流程
5. **定时同步**: 前台运行时的2分钟定时同步

### 场景冲突解决
- **互斥原则**: 同时只能执行一个场景流程
- **优先级原则**: 用户主动操作(登录/登出)优先级最高
- **状态同步**: 场景执行完成后同步认证状态和会话状态

### 离线模式支持
**最小可用功能**:
- 显示上次成功同步的健康数据
- 权限状态显示为"--"并提示网络异常
- 基本UI导航功能正常
- 网络恢复后自动重新同步数据

---

## 📋 关键业务规则

### 双Token认证规则
- **Access-Token有效期**: 15分钟，用于API访问认证
- **Refresh-Token有效期**: 14天，用于Token刷新
- **自动刷新时机**: 剩余时间≤7.5分钟时触发
- **设备绑定策略**: 基于device_id的单设备登录
- **Token轮换机制**: 每次刷新返回全新Token对，旧Token立即失效

### 健康数据管理规则
- **健康数据不使用缓存**: 权限检查和数据同步都必须实时进行，确保数据真实性
- **权限独立性**: 步数、距离、卡路里权限完全独立检查和处理
- **失败不阻塞**: 权限检查或数据同步失败时不阻塞app使用
- **占位符显示**: 未授权或失败时显示"--"占位符和友好提示
- **用户引导**: 提供清晰的权限授权引导，但不强制用户授权

### 会话管理规则
- **4小时超时规则**: 超过4小时无活动自动创建新会话
- **会话ID唯一性**: 使用UUID v4确保全局唯一性
- **会话恢复优先**: 应用启动时优先恢复有效会话，失败则创建新会话
- **状态同步**: 会话状态与认证状态保持同步，一方失效时同步清理
- **简化存储**: 仅存储核心会话数据，避免过度复杂的状态管理
- **失败降级**: 会话操作失败时不阻塞主流程，使用默认状态继续

### 健康数据计算规则
- **增量计算公式**: 会话内增量 = 当天总量 - 会话基线
- **累计计算规则**: 当天总增量 = Σ所有会话内增量
- **幂等性保证**: 多次同步相同数据不重复累加
- **异常数据隔离**: 异常数据不参与正常计算和奖励发放

### 基线会话集成规则
- **基线会话关联**: 每个基线数据与sessionId关联，支持会话追溯
- **基线初始化规则**: 会话创建时自动初始化已授权权限的基线
- **基线计算公式**: 基线 = HKStatisticsQuery(当天00:00, 会话开始时间)
- **基线不变性**: 会话内基线固定不变，确保计算一致性
- **基线恢复机制**: 会话恢复时自动恢复对应的基线数据
- **基线异常处理**: 基线设置失败不影响会话创建，记录异常状态
- **基线清理规则**: 会话结束时可选择保留基线用于数据分析
- **基线版本管理**: 支持基线数据的版本控制和历史追踪

---

## ⚠️ 错误处理机制

### 错误分类和处理策略

#### � 关键错误 (阻塞性)
**特征**: 影响核心功能，必须解决才能继续
- **认证失败**: 跳转登录页面，清除本地Token
- **设备冲突**: 显示冲突提示，强制重新登录
- **网络完全不可用**: 显示错误信息，提供重试选项

#### ⚠️ 警告错误 (降级性)
**特征**: 影响部分功能，可以降级处理
- **会话创建失败**: 使用临时会话模式，后台重试创建
- **会话状态同步失败**: 使用本地缓存状态，定期重试同步
- **基线设置失败**: 记录异常状态，不影响会话正常运行
- **单个权限检查失败**: 该权限显示"--"，其他权限正常
- **健康数据同步失败**: 使用上次成功数据，后台重试
- **Token刷新失败**: 记录错误，下次启动时重新认证

#### ℹ️ 信息错误 (可忽略)
**特征**: 不影响核心功能，静默处理
- **非关键日志上报失败**: 记录本地日志，不影响用户体验
- **统计数据收集失败**: 静默忽略，不显示错误信息

### 错误恢复机制
- **重试策略**: 指数退避重试，最多3次
- **降级处理**: 关键功能失败时的安全降级
- **状态恢复**: 异常后的状态一致性恢复
- **用户引导**: 友好的错误信息和解决建议

---

## 💡 开发指导原则

### 架构设计原则
- **职责单一**: 每个组件专注核心功能，避免职责过载
- **状态统一**: 使用统一的状态管理，避免状态分散和不一致
- **错误隔离**: 建立完善的错误边界，确保系统健壮性
- **用户优先**: 优先保证用户体验，错误不阻塞核心功能

### 开发最佳实践
- **基于真实状态**: 开发时基于实际系统状态，不依赖过时文档
- **渐进式改进**: 优先修复核心问题，逐步优化性能
- **充分测试**: 每个场景都要有对应的测试用例
- **文档同步**: 代码变更时同步更新相关文档

### 常见问题避免
- **避免会话状态不一致**: 使用SessionManager统一管理会话状态
- **避免会话数据丢失**: 实时持久化会话状态，建立恢复机制
- **避免会话创建失败**: 提供降级策略，确保应用正常运行
- **避免基线会话耦合**: 保持基线管理与会话生命周期的独立性
- **避免重复检查**: 合理使用状态传递，避免重复权限检查
- **避免阻塞用户**: 异步处理耗时操作，提供友好反馈

### 调试和监控
- **会话监控**: 监控会话创建成功率、持续时间、异常频率
- **会话诊断**: 提供会话状态一致性检查和问题根因分析
- **基线监控**: 监控基线设置成功率和数据准确性
- **日志记录**: 关键步骤都要有详细日志，包含sessionId追踪
- **性能监控**: 监控各步骤的执行时间和会话操作性能
- **错误追踪**: 记录和分析错误发生的原因，关联会话上下文
- **用户反馈**: 收集用户使用过程中的问题，提供会话诊断信息

---

## � 文档总结

### 核心价值
本文档提供了SweatMint登录与健康数据处理的**优化流程指南**，确保：
- **数据真实性**: 健康数据不使用缓存，实时获取确保数据准确性
- **用户体验优先**: 即使数据准备失败也能进入UI，通过占位符处理异常
- **统一3步骤流程**: 所有场景都遵循认证验证→数据准备→界面加载
- **简化设计**: 移除过度设计，专注核心功能和用户体验
- **完整场景覆盖**: 包含登录、登出、启动、唤醒、定时同步等所有场景
- **离线模式支持**: 提供基本的离线功能，确保应用可用性
- **错误处理完善**: 分级错误处理，优先保证用户体验

### 架构优势
- **数据真实性保证**: 健康数据不使用缓存，确保激励计算的准确性
- **用户体验优先**: 失败时不阻塞UI，通过占位符和友好提示处理异常
- **统一流程设计**: 所有场景都遵循3步骤流程，降低开发和维护复杂度
- **简化组件职责**: 移除过度设计，每个组件专注核心功能
- **完整场景覆盖**: 涵盖所有用户使用场景，包括离线模式
- **灵活错误处理**: 分级错误处理机制，确保应用在各种异常情况下的可用性

### 使用建议
1. **开发时**: 严格遵循健康数据不使用缓存原则，确保数据真实性
2. **测试时**: 重点测试各种异常情况下的UI降级处理
3. **用户体验**: 优先保证UI能正常加载，通过占位符处理数据异常
4. **场景测试**: 验证登录、登出、启动、唤醒、定时同步等所有场景
5. **离线测试**: 确保离线模式下的基本功能可用性

### 后续计划
- **实施v2.0重构**: 基于本文档实施统一状态管理架构
- **性能优化**: 减少重复检查，提升启动速度
- **监控完善**: 建立完整的监控和诊断体系
- **文档维护**: 随着重构进展同步更新文档

---

**文档状态**: ✅ 已优化 (v2.3最终版)
**适用版本**: 基于移动应用最佳实践 + 数据真实性保证
**维护周期**: 随代码变更同步更新
**技术支持**: 立即可用，经过全面技术审查验证